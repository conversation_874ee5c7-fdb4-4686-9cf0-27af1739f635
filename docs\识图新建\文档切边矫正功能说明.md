# 文档切边矫正功能说明

## 功能概述
文档切边矫正功能为用户提供图片预处理能力，通过调用TextIn的文档切边矫正API，自动检测文档边缘并进行形变矫正，返回矫正后的图片和4个角点坐标供APP端使用。

## 技术架构

### 核心组件
- **CropEnhanceController**: 文档切边矫正接口控制器
- **CropEnhanceService**: 文档切边矫正业务逻辑服务
- **CropEnhanceApiClient**: TextIn切边矫正API客户端
- **CropEnhanceResponse**: TextIn API响应DTO

### 功能特点
- **只矫正不切边**: 默认不执行切边操作，只进行文档形变矫正
- **返回矫正图片**: 提供矫正后图片的base64编码
- **返回角点坐标**: 提供4个角点坐标用于APP端显示裁剪框
- **支持增强模式**: 可选择不同的图片增强效果

## API接口

### 文档矫正接口
```
POST /api/v2/crop-enhance
Content-Type: multipart/form-data

请求参数:
- file: 图片文件 (必填)
- enhanceMode: 增强模式 (可选，默认-1)
  * -1: 禁用增强
  * 1: 增亮
  * 2: 增强并锐化
  * 3: 黑白
  * 4: 灰度
  * 5: 去阴影增强
  * 6: 点阵图
- cropImage: 是否切边 (可选，默认0)
  * 0: 不执行切边操作
  * 1: 执行切边操作

响应格式:
{
  "success": true,
  "code": "200",
  "msg": "文档矫正成功",
  "data": {
    "originalImage": {
      "width": 2000,
      "height": 3000
    },
    "correctedImage": {
      "base64": "矫正后图片的base64编码",
      "width": 1800,
      "height": 2600
    },
    "cropPoints": [
      {"x": 100, "y": 150},   // 左上角
      {"x": 1800, "y": 120},  // 右上角
      {"x": 1850, "y": 2800}, // 右下角
      {"x": 80, "y": 2850}    // 左下角
    ],
    "angle": 0,
    "processingTime": 800
  }
}
```

### 健康检查接口
```
GET /api/v2/crop-enhance/health

响应格式:
{
  "success": true,
  "code": "200",
  "msg": "文档切边矫正服务运行正常",
  "data": {
    "service": "crop-enhance",
    "status": "healthy",
    "timestamp": 1692345678901,
    "config": {
      "apiUrl": "configured",
      "apiKey": "configured",
      "appId": "configured"
    }
  }
}
```

## 配置说明

### 必需配置
在`common_config.txt`中添加：
```properties
# TextIn 文档切边矫正API配置
textin.crop.api.url=https://api.textin.com/ai/service/v1/crop_enhance_image

# 复用现有TextIn配置
textin.api.key=your_textin_api_key
textin.app.id=your_textin_app_id
```

## 使用流程

### 用户操作流程
1. **用户拍照**: 用户使用APP拍摄文档图片
2. **上传矫正**: 调用`/api/v2/crop-enhance`接口上传图片
3. **显示结果**: APP端显示矫正后的图片和自动检测的裁剪框
4. **用户调整**: 用户可以调整裁剪框的位置和大小
5. **确认裁剪**: 用户确认后，APP端基于矫正后的图片进行裁剪
6. **进行识图**: 使用最终裁剪的图片调用现有的识图接口

### 数据处理流程
```
原始图片 → 文档矫正 → 返回矫正图片(base64) + 角点坐标 → 
APP端显示 → 用户调整裁剪框 → APP端裁剪 → 识图处理
```

## 错误处理

### 常见错误
- **文件格式不支持**: 只支持jpg、jpeg、png、bmp、webp格式
- **文件过大**: 最大支持10MB文件
- **无法检测边缘**: 图片中没有清晰的文档边缘
- **API调用失败**: TextIn服务不可用或配置错误

### 错误响应格式
```json
{
  "success": false,
  "code": "501",
  "msg": "具体错误信息"
}
```

## 性能特点

- **处理速度**: 通常在1-3秒内完成矫正
- **文件限制**: 支持最大10MB图片文件
- **格式支持**: jpg、jpeg、png、bmp、webp
- **尺寸要求**: 图像宽高须介于20和10000像素之间

## 与现有功能的关系

- **独立功能**: 与现有识图接口完全分离，不影响现有功能
- **预处理工具**: 作为识图前的可选预处理步骤
- **用户选择**: 用户可以选择是否使用矫正功能
- **质量提升**: 矫正后的图片通常能提高识图准确率

## 测试建议

1. **功能测试**: 使用不同类型的文档图片测试矫正效果
2. **边界测试**: 测试文件大小限制、格式限制等边界条件
3. **错误测试**: 测试各种错误场景的处理
4. **性能测试**: 测试不同大小图片的处理时间
5. **集成测试**: 测试与现有识图功能的配合使用

## 部署注意事项

1. **配置检查**: 确保TextIn API配置正确
2. **网络连接**: 确保服务器能访问TextIn API
3. **存储空间**: 确保有足够空间处理临时文件
4. **日志监控**: 关注API调用成功率和响应时间
5. **错误处理**: 建立完善的错误监控和告警机制
