文档图像切边增强矫正
功能描述

文档图像切边增强矫正


文档图像切边矫正与图片切边增强合并产品；边缘背景去除，文档图像形变矫正，增强显示效果。

请求URL
https://api.textin.com/ai/service/v1/crop_enhance_image

HTTP请求方法（Method）
HTTP POST

请求头说明（Request Headers）
请在HTTP请求中添加以下自定义标头（Header）。

header 名	值
x-ti-app-id	请登录后前往 “工作台-账号设置-开发者信息” 查看 x-ti-app-id
x-ti-secret-code	请登录后前往 “工作台-账号设置-开发者信息” 查看 x-ti-secret-code
URL参数（Parameters）
URL参数指以 {参数名}={参数值} 形式拼接到 URL 上的键值对。它以 ? 开头，不同参数之间使用 & 连接。形如 ?p1=v1&p2=v2
参数名	数据类型	是否必填	允许的值	描述
enhance_mode	integer	否	-1, 1, 2, 3, 4, 5, 6	
1 增亮
2 增强并锐化
3 黑白
4 灰度
5 去阴影增强
6 点阵图
-1 禁用增强，默认为-1
crop_image	integer	否	0, 1	
0 不执行切边操作
1 执行切边操作，默认为1
only_position	integer	否	0, 1	
0 返回角点坐标及处理图，默认为0
1 仅返回切边角点，不返回切边结果图
dewarp_image	integer	否	0, 1	
0 不执行矫正操作
1 执行矫正操作，默认为1
deblur_image	integer	否	0, 1	
0 不执行清晰度提升操作，默认为0
1 执行清晰度提升操作
correct_direction	integer	否	0, 1	
0 不校正图片方向，默认为0
1 校正图片方向
round_image	integer	否	0, 1	
0 不返回圆角切边，默认为0
1 返回圆角切边结果
jpeg_quality	integer	否	见描述	
支持切边图片压缩率设置，建议设置范围65~100；不设置则默认为95

size_and_positon	string	否	见描述	
支持客户端传入宽高及座标,根据传的切图宽高和座标点来切边 格式 width,height,x1,y1,x2,y2,x3,y3,x4,y4

width：图像宽，height：图像高，设置成0则使用默认值
(x1, y1) 左上角坐标
(x2, y2) 右上角坐标
(x3, y3) 右下角坐标
(x4, y4) 左下角坐标
请求体说明（Request Body）
支持以下两种请求格式

1. Content-Type: application/octet-stream

要上传的图片，目前支持jpg, png, bmp, webp, pdf, tiff, 单帧gif等大部分格式。

请注意，请求体的数据格式为本地文件的二进制流，非 FormData 或其他格式。文件大小不超过 50M，图像宽高须介于 20 和 10000（像素）之间。

2. Content-Type: text/plain

请求体的数据格式为文本，内容为在线文件的URL链接（支持http以及https协议）。在线文件大小不超过 50M，图像宽高须介于 20 和 10000（像素）之间。

响应体说明 （Response）
Content-Type: application/json

JSON结构说明如下：

说明：所有接口响应中均包含字段 x_request_id（string类型），作为请求的唯一标识。

字段名	类型	描述
code	integer	错误码，详见“错误码说明”
message	string	
错误信息

version	string	
接口版本号

duration	number	
接口耗时计算，时间单位是毫秒(ms)

result	object	
  + origin_width	integer	
原图的宽

  + origin_height	integer	
原图的高

  + image_list	array	
处理后的图像内容

   ++ cropped_width	integer	
图像处理后的宽

   ++ cropped_height	integer	
图像处理后的高

   ++ image	string	
图像处理后的jpg图片，base64格式

   ++ position	array	
切图区域的4个角点坐标, 是个长度为8的数组

[0,1,2,3,4,5,6,7]

(0,1) 左上角坐标
(2,3) 右上角坐标
(4,5) 右下角坐标
(6,7) 左下角坐标
   ++ angle	integer	
图像角度，correct_direction=1时生效 定义0度为人类阅读文字的图像方向，称为正置图像， 本字段表示输入图像是正置图像进行顺时针若干角度的旋转所得。

0: ▲
90: ▶
180: ▼
270: ◀
-1 失败
JSON结构示例
{
"version":"1.0.0",
"duration":100,
"result":{
"origin_width":2000,
"origin_height":3000,
"image_list":[
{
"cropped_width":1500,
"cropped_height":1800,
"image":"/9j/4AAQSkZJRgABAQAAAQABAAD/2wBD",
"position":[
0,
10,
500,
10,
500,
300,
0,
300
],
"angle":90
}
]
}
}
错误码说明
错误码	描述
40101	x-ti-app-id 或 x-ti-secret-code 为空
40102	x-ti-app-id 或 x-ti-secret-code 无效，验证失败
40103	客户端IP不在白名单
40003	余额不足，请充值后再使用
40004	参数错误，请查看技术文档，检查传参
40007	机器人不存在或未发布
40008	机器人未开通，请至市场开通后重试
40301	文件类型不支持，接口会返回实际检测到的文件类型，如“当前文件类型为.gif”
40302	上传文件大小不符，文件大小不超过 50M
40303	文件类型不支持
40304	图片尺寸不符，图像宽高须介于 20 和 10000（像素）之间
40305	识别文件未上传
40306	QPS超过限制，收到此状态码时请勿重试，持续请求可能触发IP流控，如需扩容请联系商务
40400	无效的请求链接，请检查链接是否正确
30203	基础服务故障，请稍后重试
500	服务器内部错误