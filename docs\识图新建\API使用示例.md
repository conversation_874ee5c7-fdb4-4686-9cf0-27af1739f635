# 文档切边矫正API使用示例

## 接口地址
```
POST /api/v2/crop-enhance
```

## 请求示例

### 使用curl命令
```bash
curl -X POST \
  http://your-server.com/api/v2/crop-enhance \
  -H 'Content-Type: multipart/form-data' \
  -H 'x-access-token: your_access_token' \
  -F 'file=@/path/to/your/document.jpg' \
  -F 'enhanceMode=-1' \
  -F 'cropImage=0'
```

### 使用JavaScript (前端)
```javascript
// 创建FormData对象
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('enhanceMode', '-1'); // 可选参数
formData.append('cropImage', '0'); // 可选参数，0=不切边，1=切边

// 发送请求
fetch('/api/v2/crop-enhance', {
    method: 'POST',
    headers: {
        'x-access-token': 'your_access_token'
    },
    body: formData
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('矫正成功:', data.data);
        
        // 显示矫正后的图片
        const correctedImage = data.data.correctedImage;
        const imgElement = document.createElement('img');
        imgElement.src = 'data:image/jpeg;base64,' + correctedImage.base64;
        document.body.appendChild(imgElement);
        
        // 获取角点坐标用于绘制裁剪框
        const cropPoints = data.data.cropPoints;
        console.log('角点坐标:', cropPoints);
        // cropPoints格式: [
        //   {x: 100, y: 150},   // 左上角
        //   {x: 1800, y: 120},  // 右上角
        //   {x: 1850, y: 2800}, // 右下角
        //   {x: 80, y: 2850}    // 左下角
        // ]
        
    } else {
        console.error('矫正失败:', data.msg);
    }
})
.catch(error => {
    console.error('请求失败:', error);
});
```

### 使用Java (Android)
```java
// 使用OkHttp发送请求
OkHttpClient client = new OkHttpClient();

// 构建请求体
RequestBody fileBody = RequestBody.create(
    MediaType.parse("image/jpeg"), 
    imageFile
);

MultipartBody requestBody = new MultipartBody.Builder()
    .setType(MultipartBody.FORM)
    .addFormDataPart("file", "document.jpg", fileBody)
    .addFormDataPart("enhanceMode", "-1")
    .addFormDataPart("cropImage", "0")
    .build();

// 构建请求
Request request = new Request.Builder()
    .url("http://your-server.com/api/v2/crop-enhance")
    .addHeader("x-access-token", "your_access_token")
    .post(requestBody)
    .build();

// 发送请求
client.newCall(request).enqueue(new Callback() {
    @Override
    public void onFailure(Call call, IOException e) {
        Log.e("CropEnhance", "请求失败", e);
    }

    @Override
    public void onResponse(Call call, Response response) throws IOException {
        if (response.isSuccessful()) {
            String responseBody = response.body().string();
            // 解析JSON响应
            JSONObject jsonResponse = new JSONObject(responseBody);
            
            if (jsonResponse.getBoolean("success")) {
                JSONObject data = jsonResponse.getJSONObject("data");
                
                // 获取矫正后的图片
                JSONObject correctedImage = data.getJSONObject("correctedImage");
                String base64Image = correctedImage.getString("base64");
                
                // 解码base64图片
                byte[] imageBytes = Base64.decode(base64Image, Base64.DEFAULT);
                Bitmap bitmap = BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.length);
                
                // 获取角点坐标
                JSONArray cropPoints = data.getJSONArray("cropPoints");
                List<Point> points = new ArrayList<>();
                for (int i = 0; i < cropPoints.length(); i++) {
                    JSONObject point = cropPoints.getJSONObject(i);
                    points.add(new Point(point.getInt("x"), point.getInt("y")));
                }
                
                // 在UI线程中更新界面
                runOnUiThread(() -> {
                    // 显示矫正后的图片
                    imageView.setImageBitmap(bitmap);
                    
                    // 绘制裁剪框
                    drawCropBox(points);
                });
            }
        }
    }
});
```

## 响应示例

### 成功响应
```json
{
  "success": true,
  "code": "200",
  "msg": "文档矫正成功",
  "data": {
    "originalImage": {
      "width": 2000,
      "height": 3000
    },
    "correctedImage": {
      "base64": "/9j/4AAQSkZJRgABAQAAAQABAAD/2wBD...(base64编码的图片数据)",
      "width": 1800,
      "height": 2600
    },
    "cropPoints": [
      {"x": 100, "y": 150},
      {"x": 1800, "y": 120},
      {"x": 1850, "y": 2800},
      {"x": 80, "y": 2850}
    ],
    "angle": 0,
    "processingTime": 1200
  }
}
```

### 错误响应
```json
{
  "success": false,
  "code": "501",
  "msg": "不支持的图片格式，请上传jpg、png、bmp、webp格式的图片"
}
```

## 参数说明

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| file | File | 是 | 要矫正的图片文件 |
| enhanceMode | Integer | 否 | 增强模式，默认-1 |
| cropImage | Integer | 否 | 是否切边，默认0 |

### enhanceMode取值说明
| 值 | 说明 |
|----|------|
| -1 | 禁用增强(默认) |
| 1 | 增亮 |
| 2 | 增强并锐化 |
| 3 | 黑白 |
| 4 | 灰度 |
| 5 | 去阴影增强 |
| 6 | 点阵图 |

### cropImage取值说明
| 值 | 说明 |
|----|------|
| 0 | 不执行切边操作(默认) |
| 1 | 执行切边操作 |

### 响应字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | Boolean | 是否成功 |
| code | String | 响应码，成功为"200"，失败为"501" |
| msg | String | 响应消息 |
| data.originalImage | Object | 原图信息 |
| data.originalImage.width | Integer | 原图宽度 |
| data.originalImage.height | Integer | 原图高度 |
| data.correctedImage | Object | 矫正后图片信息 |
| data.correctedImage.base64 | String | 矫正后图片的base64编码 |
| data.correctedImage.width | Integer | 矫正后图片宽度 |
| data.correctedImage.height | Integer | 矫正后图片高度 |
| data.cropPoints | Array | 4个角点坐标数组 |
| data.cropPoints[].x | Integer | 角点x坐标 |
| data.cropPoints[].y | Integer | 角点y坐标 |
| data.angle | Integer | 文档旋转角度 |
| data.processingTime | Long | 处理耗时(毫秒) |

## 健康检查

### 检查服务状态
```bash
curl -X GET http://your-server.com/api/v2/crop-enhance/health
```

### 响应示例
```json
{
  "success": true,
  "code": "200",
  "msg": "文档切边矫正服务运行正常",
  "data": {
    "service": "crop-enhance",
    "status": "healthy",
    "timestamp": 1692345678901,
    "config": {
      "apiUrl": "configured",
      "apiKey": "configured",
      "appId": "configured"
    }
  }
}
```

## 注意事项

1. **文件大小限制**: 最大支持10MB的图片文件
2. **格式支持**: 支持jpg、jpeg、png、bmp、webp格式
3. **认证要求**: 需要在请求头中包含有效的access token
4. **Base64处理**: 返回的图片是base64编码，需要解码后使用
5. **角点顺序**: 角点按照左上、右上、右下、左下的顺序返回
6. **坐标系统**: 角点坐标基于矫正后图片的像素坐标系
