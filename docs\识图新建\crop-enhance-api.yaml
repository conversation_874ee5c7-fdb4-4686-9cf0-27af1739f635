openapi: 3.0.3
info:
  title: XPrinter 文档切边矫正 API
  description: |
    XPrinter 文档切边矫正功能API接口文档
    
    ## 功能说明
    - 提供文档图片的形变矫正功能
    - 自动检测文档边缘并返回4个角点坐标
    - 返回矫正后的图片base64编码
    - 支持多种图片增强模式
    
    ## 技术特点
    - 只矫正不切边，保持原图完整性
    - 基于TextIn AI技术，识别准确率高
    - 支持多种图片格式和增强模式
    - 提供完整的错误处理和状态检查
  version: 1.0.0
  contact:
    name: XPrinter API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8080
    description: 本地开发环境
  - url: https://api.xprinter.com
    description: 生产环境

tags:
  - name: 文档切边矫正
    description: 文档图片矫正和角点检测相关接口

paths:
  /api/v2/crop-enhance:
    post:
      tags:
        - 文档切边矫正
      summary: 文档矫正和角点检测
      description: |
        上传文档图片，进行形变矫正并检测文档边缘角点
        
        ## 功能特点
        - 自动矫正文档形变和倾斜
        - 检测文档边缘的4个角点坐标
        - 返回矫正后的图片base64编码
        - 支持可选的图片增强处理
        
        ## 使用场景
        - 拍照文档的预处理
        - 为后续OCR识别提供更好的图片质量
        - 为用户提供智能裁剪框参考
      operationId: cropEnhance
      security:
        - ApiKeyAuth: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - file
              properties:
                file:
                  type: string
                  format: binary
                  description: |
                    要矫正的图片文件
                    
                    **支持格式**: jpg, jpeg, png, bmp, webp
                    **文件大小**: 最大10MB
                    **图片尺寸**: 宽高须介于20和10000像素之间
                enhanceMode:
                  type: integer
                  description: |
                    图片增强模式（可选）
                    
                    - `-1`: 禁用增强（默认）
                    - `1`: 增亮
                    - `2`: 增强并锐化  
                    - `3`: 黑白
                    - `4`: 灰度
                    - `5`: 去阴影增强
                    - `6`: 点阵图
                  default: -1
                  enum: [-1, 1, 2, 3, 4, 5, 6]
                  example: -1
            encoding:
              file:
                contentType: image/jpeg, image/png, image/bmp, image/webp
      responses:
        '200':
          description: 矫正成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CropEnhanceSuccessResponse'
              examples:
                success:
                  summary: 矫正成功示例
                  value:
                    success: true
                    code: "200"
                    msg: "文档矫正成功"
                    data:
                      originalImage:
                        width: 2000
                        height: 3000
                      correctedImage:
                        base64: "/9j/4AAQSkZJRgABAQAAAQABAAD/2wBD..."
                        width: 1800
                        height: 2600
                      cropPoints:
                        - x: 100
                          y: 150
                        - x: 1800
                          y: 120
                        - x: 1850
                          y: 2800
                        - x: 80
                          y: 2850
                      angle: 0
                      processingTime: 1200
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                no_file:
                  summary: 未上传文件
                  value:
                    success: false
                    code: "501"
                    msg: "请上传图片文件"
                file_too_large:
                  summary: 文件过大
                  value:
                    success: false
                    code: "501"
                    msg: "图片文件过大，最大支持10MB"
                invalid_format:
                  summary: 格式不支持
                  value:
                    success: false
                    code: "501"
                    msg: "不支持的图片格式，请上传jpg、png、bmp、webp格式的图片"
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                success: false
                code: "501"
                msg: "用户未登录或token已过期"
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                textin_api_error:
                  summary: TextIn API调用失败
                  value:
                    success: false
                    code: "501"
                    msg: "TextIn API调用失败，请稍后重试"
                processing_error:
                  summary: 图片处理异常
                  value:
                    success: false
                    code: "501"
                    msg: "文档矫正失败: 无法检测到文档边缘，请确保图片中包含完整的文档内容"

  /api/v2/crop-enhance/health:
    get:
      tags:
        - 文档切边矫正
      summary: 健康检查接口
      description: |
        检查文档切边矫正服务是否正常运行
        
        ## 检查内容
        - 服务运行状态
        - TextIn API配置状态
        - 系统时间戳
      operationId: healthCheck
      responses:
        '200':
          description: 服务正常
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'
              examples:
                healthy:
                  summary: 服务正常
                  value:
                    success: true
                    code: "200"
                    msg: "文档切边矫正服务运行正常"
                    data:
                      service: "crop-enhance"
                      status: "healthy"
                      timestamp: 1692345678901
                      config:
                        apiUrl: "configured"
                        apiKey: "configured"
                        appId: "configured"
                config_missing:
                  summary: 配置缺失
                  value:
                    success: true
                    code: "200"
                    msg: "配置缺失"
                    data:
                      service: "crop-enhance"
                      status: "config_missing"
                      timestamp: 1692345678901
                      config:
                        apiUrl: "missing"
                        apiKey: "configured"
                        appId: "configured"
        '500':
          description: 服务异常
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                success: false
                code: "501"
                msg: "健康检查失败: 服务内部错误"

components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: x-access-token
      description: |
        用户访问令牌
        
        通过登录接口获取，在请求头中传递：
        ```
        x-access-token: your_access_token_here
        ```

  schemas:
    CropEnhanceSuccessResponse:
      type: object
      required:
        - success
        - code
        - msg
        - data
      properties:
        success:
          type: boolean
          example: true
          description: 请求是否成功
        code:
          type: string
          example: "200"
          description: 响应状态码
        msg:
          type: string
          example: "文档矫正成功"
          description: 响应消息
        data:
          $ref: '#/components/schemas/CropEnhanceData'

    CropEnhanceData:
      type: object
      required:
        - originalImage
        - correctedImage
        - cropPoints
        - angle
        - processingTime
      properties:
        originalImage:
          $ref: '#/components/schemas/ImageInfo'
        correctedImage:
          $ref: '#/components/schemas/CorrectedImageInfo'
        cropPoints:
          type: array
          items:
            $ref: '#/components/schemas/Point'
          description: 文档边缘的4个角点坐标（按左上、右上、右下、左下顺序）
          minItems: 4
          maxItems: 4
        angle:
          type: integer
          example: 0
          description: |
            文档旋转角度
            - 0: ▲ 正置
            - 90: ▶ 顺时针90度
            - 180: ▼ 倒置
            - 270: ◀ 逆时针90度
            - -1: 检测失败
        processingTime:
          type: integer
          example: 1200
          description: 处理耗时（毫秒）

    ImageInfo:
      type: object
      required:
        - width
        - height
      properties:
        width:
          type: integer
          example: 2000
          description: 图片宽度（像素）
        height:
          type: integer
          example: 3000
          description: 图片高度（像素）

    CorrectedImageInfo:
      type: object
      required:
        - base64
        - width
        - height
      properties:
        base64:
          type: string
          example: "/9j/4AAQSkZJRgABAQAAAQABAAD/2wBD..."
          description: 矫正后图片的base64编码（JPEG格式）
        width:
          type: integer
          example: 1800
          description: 矫正后图片宽度（像素）
        height:
          type: integer
          example: 2600
          description: 矫正后图片高度（像素）

    Point:
      type: object
      required:
        - x
        - y
      properties:
        x:
          type: integer
          example: 100
          description: X坐标（像素）
        y:
          type: integer
          example: 150
          description: Y坐标（像素）

    HealthResponse:
      type: object
      required:
        - success
        - code
        - msg
        - data
      properties:
        success:
          type: boolean
          example: true
          description: 请求是否成功
        code:
          type: string
          example: "200"
          description: 响应状态码
        msg:
          type: string
          example: "文档切边矫正服务运行正常"
          description: 响应消息
        data:
          $ref: '#/components/schemas/HealthData'

    HealthData:
      type: object
      required:
        - service
        - status
        - timestamp
        - config
      properties:
        service:
          type: string
          example: "crop-enhance"
          description: 服务名称
        status:
          type: string
          enum: [healthy, config_missing, error]
          example: "healthy"
          description: 服务状态
        timestamp:
          type: integer
          example: 1692345678901
          description: 检查时间戳
        config:
          type: object
          properties:
            apiUrl:
              type: string
              enum: [configured, missing]
              example: "configured"
              description: API URL配置状态
            apiKey:
              type: string
              enum: [configured, missing]
              example: "configured"
              description: API Key配置状态
            appId:
              type: string
              enum: [configured, missing]
              example: "configured"
              description: App ID配置状态

    ErrorResponse:
      type: object
      required:
        - success
        - code
        - msg
      properties:
        success:
          type: boolean
          example: false
          description: 请求是否成功
        code:
          type: string
          example: "501"
          description: 错误状态码
        msg:
          type: string
          example: "具体错误信息"
          description: 错误消息
