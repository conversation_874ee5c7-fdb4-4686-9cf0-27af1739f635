# 切边参数测试指南

## 概述
现在文档切边矫正接口已经支持 `cropImage` 参数，你可以通过这个参数来控制是否执行切边操作，方便测试和调试不同的效果。

## 参数说明

### cropImage 参数
- **类型**: Integer
- **必填**: 否
- **默认值**: 0（不切边）
- **取值范围**:
  - `0`: 不执行切边操作（只矫正形变）
  - `1`: 执行切边操作（矫正+切边）

## 测试场景

### 场景1：只矫正不切边（默认行为）
```bash
curl -X POST \
  http://localhost:8080/api/v2/crop-enhance \
  -H 'Content-Type: multipart/form-data' \
  -H 'x-access-token: your_token' \
  -F 'file=@test-document.jpg' \
  -F 'enhanceMode=-1' \
  -F 'cropImage=0'
```

**预期效果**：
- 返回矫正后的完整图片
- 图片尺寸可能与原图相同或略有变化
- 4个角点坐标用于显示裁剪框
- 适合让用户手动调整裁剪区域

### 场景2：矫正并自动切边
```bash
curl -X POST \
  http://localhost:8080/api/v2/crop-enhance \
  -H 'Content-Type: multipart/form-data' \
  -H 'x-access-token: your_token' \
  -F 'file=@test-document.jpg' \
  -F 'enhanceMode=-1' \
  -F 'cropImage=1'
```

**预期效果**：
- 返回矫正并切边后的图片
- 图片尺寸通常比原图小（只保留文档区域）
- 4个角点坐标相对于切边后的图片
- 适合直接用于后续识图处理

### 场景3：结合增强模式测试
```bash
# 矫正+切边+黑白增强
curl -X POST \
  http://localhost:8080/api/v2/crop-enhance \
  -H 'Content-Type: multipart/form-data' \
  -H 'x-access-token: your_token' \
  -F 'file=@test-document.jpg' \
  -F 'enhanceMode=3' \
  -F 'cropImage=1'

# 矫正+不切边+去阴影增强
curl -X POST \
  http://localhost:8080/api/v2/crop-enhance \
  -H 'Content-Type: multipart/form-data' \
  -H 'x-access-token: your_token' \
  -F 'file=@test-document.jpg' \
  -F 'enhanceMode=5' \
  -F 'cropImage=0'
```

## JavaScript 测试示例

```javascript
// 测试函数
async function testCropEnhance(file, enhanceMode = -1, cropImage = 0) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('enhanceMode', enhanceMode.toString());
    formData.append('cropImage', cropImage.toString());
    
    try {
        const response = await fetch('/api/v2/crop-enhance', {
            method: 'POST',
            headers: {
                'x-access-token': 'your_token_here'
            },
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            console.log(`测试成功 - 切边模式: ${cropImage}, 增强模式: ${enhanceMode}`);
            console.log('原图尺寸:', result.data.originalImage);
            console.log('矫正后尺寸:', result.data.correctedImage);
            console.log('角点坐标:', result.data.cropPoints);
            console.log('处理时间:', result.data.processingTime + 'ms');
            
            // 显示图片
            const img = new Image();
            img.src = 'data:image/jpeg;base64,' + result.data.correctedImage.base64;
            document.body.appendChild(img);
            
        } else {
            console.error('测试失败:', result.msg);
        }
    } catch (error) {
        console.error('请求失败:', error);
    }
}

// 使用示例
const fileInput = document.getElementById('fileInput');
const file = fileInput.files[0];

// 测试不同组合
testCropEnhance(file, -1, 0);  // 只矫正
testCropEnhance(file, -1, 1);  // 矫正+切边
testCropEnhance(file, 3, 1);   // 矫正+切边+黑白
testCropEnhance(file, 5, 0);   // 矫正+去阴影（不切边）
```

## 对比测试建议

### 1. 效果对比
建议使用同一张图片测试不同参数组合，对比效果：

| 测试组合 | enhanceMode | cropImage | 用途 |
|----------|-------------|-----------|------|
| 基础矫正 | -1 | 0 | 基础形变矫正，保留完整图片 |
| 自动切边 | -1 | 1 | 自动切除背景，只保留文档 |
| 增强矫正 | 3 | 0 | 黑白增强+矫正，便于用户调整 |
| 增强切边 | 3 | 1 | 黑白增强+自动切边，直接识图 |

### 2. 性能测试
- 记录不同参数组合的处理时间
- 测试不同大小图片的处理效果
- 观察内存和CPU使用情况

### 3. 质量评估
- **矫正质量**: 文档是否正确矫正了形变和倾斜
- **切边准确性**: 自动切边是否准确识别了文档边界
- **角点精度**: 返回的4个角点是否准确标识了文档位置
- **增强效果**: 不同增强模式对后续识图的帮助程度

## 调试技巧

### 1. 查看日志
启动服务时注意观察日志输出：
```
接收到图片文件: test.jpg, 大小: 1234567 bytes, 增强模式: -1, 是否切边: 0
开始调用TextIn切边矫正API
TextIn切边矫正API调用成功
文档切边矫正成功
```

### 2. 健康检查
测试前先检查服务状态：
```bash
curl -X GET http://localhost:8080/api/v2/crop-enhance/health
```

### 3. 错误处理
如果遇到问题，检查：
- TextIn API配置是否正确
- 图片格式是否支持
- 文件大小是否超限
- 网络连接是否正常

## 推荐测试流程

1. **基础功能测试**
   - 先用 `cropImage=0` 测试基础矫正功能
   - 确认能正常返回矫正图片和角点坐标

2. **切边功能测试**
   - 再用 `cropImage=1` 测试自动切边功能
   - 对比切边前后的效果差异

3. **增强模式测试**
   - 结合不同 `enhanceMode` 值测试图片增强效果
   - 找到最适合你的文档类型的增强模式

4. **集成测试**
   - 将矫正后的图片用于后续的OCR识图
   - 验证矫正是否提高了识图准确率

通过这些测试，你可以找到最适合你应用场景的参数组合！🎯
